# Spring AI Alibaba 集成指南

本项目已成功集成 Spring AI Alibaba 1.0.0.2 版本，使用阿里云百炼DashScope服务。

## 版本信息

- **Spring Boot**: 3.4.5
- **Spring AI**: 1.0.0
- **Spring AI Alibaba**: 1.0.0.2
- **默认模型**: qwen-plus

## 配置步骤

### 1. 获取阿里云百炼API Key

1. 访问 [阿里云百炼控制台](https://dashscope.console.aliyun.com/)
2. 登录您的阿里云账号
3. 在API-KEY管理页面创建新的API Key
4. 复制生成的API Key

### 2. 配置API Key

编辑 `src/main/resources/application.properties` 文件：

```properties
# 阿里云百炼DashScope配置
spring.ai.dashscope.api-key=your-actual-api-key-here
spring.ai.dashscope.chat.options.model=qwen-plus
```

将 `your-actual-api-key-here` 替换为您的实际API Key。

### 3. 可用模型

Spring AI Alibaba支持以下通义千问模型：

- `qwen-plus` - 通义千问Plus（推荐）
- `qwen-turbo` - 通义千问Turbo（快速响应）
- `qwen-max` - 通义千问Max（最强性能）
- `qwen-long` - 通义千问Long（长文本）

## API接口

项目提供以下测试接口：

### 健康检查
```bash
GET /api/chat/health
```

### 简单聊天
```bash
POST /api/chat/simple
Content-Type: application/json

{
  "message": "你好，请介绍一下你自己"
}
```

### 详细聊天（包含元数据）
```bash
POST /api/chat/detailed
Content-Type: application/json

{
  "message": "请用一句话总结Spring AI Alibaba框架的作用"
}
```

## 运行项目

```bash
# 编译项目
./mvnw clean compile

# 启动项目
./mvnw spring-boot:run
```

项目启动后访问 http://localhost:8080

## 注意事项

1. 确保您的阿里云账户有足够的余额或已开通相关服务
2. API Key请妥善保管，不要提交到版本控制系统
3. 建议在生产环境中使用环境变量配置API Key：
   ```bash
   export SPRING_AI_DASHSCOPE_API_KEY=your-api-key
   ```
   然后在配置文件中使用：
   ```properties
   spring.ai.dashscope.api-key=${SPRING_AI_DASHSCOPE_API_KEY}
   ```

## 更多功能

Spring AI Alibaba还支持：

- 多模态对话（图片、语音）
- 函数调用（Tool Calling）
- RAG（检索增强生成）
- 向量存储
- 智能体工作流
- 对话记忆

详细文档请参考：https://java2ai.com/
