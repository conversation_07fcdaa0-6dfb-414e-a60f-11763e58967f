# 多AI平台集成指南

本项目已集成多个主流AI平台，支持统一的API接口调用不同的AI服务。

## 支持的AI平台

### 1. 阿里云百炼DashScope
- **模型**: qwen-plus, qwen-turbo, qwen-max等
- **官网**: https://dashscope.console.aliyun.com/
- **配置前缀**: `spring.ai.dashscope`

### 2. OpenAI
- **模型**: gpt-4o, gpt-4o-mini, gpt-3.5-turbo等
- **官网**: https://platform.openai.com/
- **配置前缀**: `spring.ai.openai`

### 3. DeepSeek
- **模型**: deepseek-chat, deepseek-coder等
- **官网**: https://platform.deepseek.com/
- **配置前缀**: `spring.ai.deepseek`

### 4. XAI (Grok)
- **模型**: grok-beta等
- **官网**: https://x.ai/
- **配置前缀**: `spring.ai.xai`

## 配置说明

### API密钥配置

在 `application.properties` 中配置你的API密钥：

```properties
# 阿里云百炼DashScope
spring.ai.dashscope.api-key=your-dashscope-api-key-here

# OpenAI
spring.ai.openai.api-key=your-openai-api-key-here

# DeepSeek
spring.ai.deepseek.api-key=your-deepseek-api-key-here

# XAI
spring.ai.xai.api-key=your-xai-api-key-here

# 设置默认使用的AI平台
app.ai.default-provider=dashscope
```

### 模型配置

每个平台都可以配置不同的模型和参数：

```properties
# DashScope模型配置
spring.ai.dashscope.chat.options.model=qwen-plus
spring.ai.dashscope.chat.options.temperature=0.7
spring.ai.dashscope.chat.options.max-tokens=2000

# OpenAI模型配置
spring.ai.openai.chat.options.model=gpt-4o-mini
spring.ai.openai.chat.options.temperature=0.7
spring.ai.openai.chat.options.max-tokens=2000
```

## API接口

### 1. 健康检查
```bash
GET /api/chat/health
```

返回系统状态和可用的AI平台信息。

### 2. 获取可用平台
```bash
GET /api/chat/providers
```

返回所有可用的AI平台列表。

### 3. 简单聊天（使用默认平台）
```bash
POST /api/chat/simple
Content-Type: application/json

{
  "message": "你好，请介绍一下你自己"
}
```

### 4. 简单聊天（指定平台）
```bash
POST /api/chat/simple/{provider}
Content-Type: application/json

{
  "message": "你好，请介绍一下你自己"
}
```

支持的provider值：`dashscope`, `openai`, `deepseek`, `xai`

### 5. 详细聊天（使用默认平台）
```bash
POST /api/chat/detailed
Content-Type: application/json

{
  "message": "你好，请介绍一下你自己"
}
```

### 6. 详细聊天（指定平台）
```bash
POST /api/chat/detailed/{provider}
Content-Type: application/json

{
  "message": "你好，请介绍一下你自己"
}
```

## 使用示例

### 测试不同平台

```bash
# 使用DashScope
curl -X POST http://localhost:8080/api/chat/simple/dashscope \
  -H "Content-Type: application/json" \
  -d '{"message": "你好"}'

# 使用OpenAI
curl -X POST http://localhost:8080/api/chat/simple/openai \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello"}'

# 使用DeepSeek
curl -X POST http://localhost:8080/api/chat/simple/deepseek \
  -H "Content-Type: application/json" \
  -d '{"message": "你好"}'

# 使用XAI
curl -X POST http://localhost:8080/api/chat/simple/xai \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello"}'
```

## 注意事项

1. **API密钥安全**: 请妥善保管你的API密钥，不要提交到版本控制系统中
2. **平台可用性**: 只有配置了API密钥的平台才会被激活
3. **网络访问**: 确保服务器能够访问对应的AI平台API
4. **费用控制**: 各平台都有不同的计费方式，请注意控制使用量
5. **模型选择**: 不同平台支持的模型不同，请根据需要选择合适的模型

## 故障排除

1. **平台不可用**: 检查API密钥是否正确配置
2. **网络超时**: 检查网络连接和防火墙设置
3. **模型不存在**: 确认使用的模型名称是否正确
4. **配额不足**: 检查API平台的使用配额和余额
