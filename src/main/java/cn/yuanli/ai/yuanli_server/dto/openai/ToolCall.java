package cn.yuanli.ai.yuanli_server.dto.openai;

/**
 * OpenAI工具调用
 */
public class ToolCall {
    
    /**
     * 工具调用ID
     */
    private String id;
    
    /**
     * 工具类型
     */
    private String type;
    
    /**
     * 函数调用信息
     */
    private FunctionCall function;
    
    // 构造函数
    public ToolCall() {}
    
    public ToolCall(String id, String type, FunctionCall function) {
        this.id = id;
        this.type = type;
        this.function = function;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public FunctionCall getFunction() {
        return function;
    }
    
    public void setFunction(FunctionCall function) {
        this.function = function;
    }
}
