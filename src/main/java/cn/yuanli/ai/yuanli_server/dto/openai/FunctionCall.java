package cn.yuanli.ai.yuanli_server.dto.openai;

/**
 * OpenAI函数调用
 */
public class FunctionCall {
    
    /**
     * 函数名称
     */
    private String name;
    
    /**
     * 函数参数 (JSON字符串)
     */
    private String arguments;
    
    // 构造函数
    public FunctionCall() {}
    
    public FunctionCall(String name, String arguments) {
        this.name = name;
        this.arguments = arguments;
    }
    
    // Getters and Setters
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getArguments() {
        return arguments;
    }
    
    public void setArguments(String arguments) {
        this.arguments = arguments;
    }
}
