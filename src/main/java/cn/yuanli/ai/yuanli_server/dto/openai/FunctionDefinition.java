package cn.yuanli.ai.yuanli_server.dto.openai;

import java.util.Map;

/**
 * OpenAI函数定义
 */
public class FunctionDefinition {
    
    /**
     * 函数名称
     */
    private String name;
    
    /**
     * 函数描述
     */
    private String description;
    
    /**
     * 函数参数定义 (JSON Schema格式)
     */
    private Map<String, Object> parameters;
    
    // 构造函数
    public FunctionDefinition() {}
    
    public FunctionDefinition(String name, String description, Map<String, Object> parameters) {
        this.name = name;
        this.description = description;
        this.parameters = parameters;
    }
    
    // Getters and Setters
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Map<String, Object> getParameters() {
        return parameters;
    }
    
    public void setParameters(Map<String, Object> parameters) {
        this.parameters = parameters;
    }
}
