package cn.yuanli.ai.yuanli_server.dto.openai;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * OpenAI标准聊天消息
 */
public class ChatMessage {
    
    /**
     * 消息角色: "system", "user", "assistant", "function", "tool"
     */
    private String role;
    
    /**
     * 消息内容
     */
    private String content;
    
    /**
     * 消息名称 (可选)
     */
    private String name;
    
    /**
     * 函数调用信息
     */
    @JsonProperty("function_call")
    private FunctionCall functionCall;
    
    /**
     * 工具调用信息
     */
    @JsonProperty("tool_calls")
    private List<ToolCall> toolCalls;
    
    /**
     * 工具调用ID (用于tool角色消息)
     */
    @JsonProperty("tool_call_id")
    private String toolCallId;
    
    // 构造函数
    public ChatMessage() {}
    
    public ChatMessage(String role, String content) {
        this.role = role;
        this.content = content;
    }
    
    public ChatMessage(String role, String content, String name) {
        this.role = role;
        this.content = content;
        this.name = name;
    }
    
    // Getters and Setters
    public String getRole() {
        return role;
    }
    
    public void setRole(String role) {
        this.role = role;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public FunctionCall getFunctionCall() {
        return functionCall;
    }
    
    public void setFunctionCall(FunctionCall functionCall) {
        this.functionCall = functionCall;
    }
    
    public List<ToolCall> getToolCalls() {
        return toolCalls;
    }
    
    public void setToolCalls(List<ToolCall> toolCalls) {
        this.toolCalls = toolCalls;
    }
    
    public String getToolCallId() {
        return toolCallId;
    }
    
    public void setToolCallId(String toolCallId) {
        this.toolCallId = toolCallId;
    }
}
