package cn.yuanli.ai.yuanli_server.dto.openai;

/**
 * OpenAI工具定义 (新版function calling)
 */
public class Tool {
    
    /**
     * 工具类型，目前只支持 "function"
     */
    private String type;
    
    /**
     * 函数定义
     */
    private FunctionDefinition function;
    
    // 构造函数
    public Tool() {}
    
    public Tool(String type, FunctionDefinition function) {
        this.type = type;
        this.function = function;
    }
    
    // 便捷构造函数
    public static Tool function(FunctionDefinition function) {
        return new Tool("function", function);
    }
    
    // Getters and Setters
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public FunctionDefinition getFunction() {
        return function;
    }
    
    public void setFunction(FunctionDefinition function) {
        this.function = function;
    }
}
