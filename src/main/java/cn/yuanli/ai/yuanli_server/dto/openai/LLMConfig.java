package cn.yuanli.ai.yuanli_server.dto.openai;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 用户自定义LLM配置
 */
public class LLMConfig {
    
    /**
     * 用户指定的模型名称
     */
    @JsonProperty("userModel")
    private String userModel;

    /**
     * 用户指定的API密钥
     */
    @JsonProperty("userApiKey")
    private String userApiKey;

    /**
     * 用户指定的提供商 (必须是预置支持的平台)
     */
    @JsonProperty("userProvider")
    private String userProvider;

    /**
     * 用户指定的Base URL
     */
    @JsonProperty("userBaseUrl")
    private String userBaseUrl;

    // 构造函数
    public LLMConfig() {}
    
    public LLMConfig(String userModel, String userApiKey, String userProvider) {
        this.userModel = userModel;
        this.userApiKey = userApiKey;
        this.userProvider = userProvider;
    }

    public LLMConfig(String userModel, String userApiKey, String userProvider, String userBaseUrl) {
        this.userModel = userModel;
        this.userApiKey = userApiKey;
        this.userProvider = userProvider;
        this.userBaseUrl = userBaseUrl;
    }
    
    // Getters and Setters
    public String getUserModel() {
        return userModel;
    }
    
    public void setUserModel(String userModel) {
        this.userModel = userModel;
    }
    
    public String getUserApiKey() {
        return userApiKey;
    }
    
    public void setUserApiKey(String userApiKey) {
        this.userApiKey = userApiKey;
    }
    
    public String getUserProvider() {
        return userProvider;
    }
    
    public void setUserProvider(String userProvider) {
        this.userProvider = userProvider;
    }

    public String getUserBaseUrl() {
        return userBaseUrl;
    }

    public void setUserBaseUrl(String userBaseUrl) {
        this.userBaseUrl = userBaseUrl;
    }

    /**
     * 验证用户配置是否完整
     */
    public boolean isValid() {
        return userModel != null && !userModel.trim().isEmpty() &&
               userApiKey != null && !userApiKey.trim().isEmpty() &&
               userProvider != null && !userProvider.trim().isEmpty();
    }
}
