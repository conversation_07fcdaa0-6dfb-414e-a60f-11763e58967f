package cn.yuanli.ai.yuanli_server.dto.openai;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Map;

/**
 * OpenAI标准聊天请求DTO
 */
public class OpenAIChatRequest {
    
    /**
     * 消息列表
     */
    private List<ChatMessage> messages;
    
    /**
     * 模型名称 (支持映射表中的模型)
     */
    private String model;
    
    /**
     * 温度参数 (0-2)
     */
    private Double temperature;
    
    /**
     * 最大token数
     */
    @JsonProperty("max_tokens")
    private Integer maxTokens;
    
    /**
     * 是否流式响应
     */
    private Boolean stream;
    
    /**
     * 函数调用配置
     */
    private List<FunctionDefinition> functions;
    
    /**
     * 函数调用模式: "none", "auto", 或具体函数名
     */
    @JsonProperty("function_call")
    private Object functionCall;
    
    /**
     * 工具列表 (新版function calling)
     */
    private List<Tool> tools;
    
    /**
     * 工具选择模式
     */
    @JsonProperty("tool_choice")
    private Object toolChoice;
    
    /**
     * 用户自定义LLM配置
     */
    @JsonProperty("llmConfig")
    private LLMConfig llmConfig;
    
    // 构造函数
    public OpenAIChatRequest() {}
    
    // Getters and Setters
    public List<ChatMessage> getMessages() {
        return messages;
    }
    
    public void setMessages(List<ChatMessage> messages) {
        this.messages = messages;
    }
    
    public String getModel() {
        return model;
    }
    
    public void setModel(String model) {
        this.model = model;
    }
    
    public Double getTemperature() {
        return temperature;
    }
    
    public void setTemperature(Double temperature) {
        this.temperature = temperature;
    }
    
    public Integer getMaxTokens() {
        return maxTokens;
    }
    
    public void setMaxTokens(Integer maxTokens) {
        this.maxTokens = maxTokens;
    }
    
    public Boolean getStream() {
        return stream;
    }
    
    public void setStream(Boolean stream) {
        this.stream = stream;
    }
    
    public List<FunctionDefinition> getFunctions() {
        return functions;
    }
    
    public void setFunctions(List<FunctionDefinition> functions) {
        this.functions = functions;
    }
    
    public Object getFunctionCall() {
        return functionCall;
    }
    
    public void setFunctionCall(Object functionCall) {
        this.functionCall = functionCall;
    }
    
    public List<Tool> getTools() {
        return tools;
    }
    
    public void setTools(List<Tool> tools) {
        this.tools = tools;
    }
    
    public Object getToolChoice() {
        return toolChoice;
    }
    
    public void setToolChoice(Object toolChoice) {
        this.toolChoice = toolChoice;
    }
    
    public LLMConfig getLlmConfig() {
        return llmConfig;
    }
    
    public void setLlmConfig(LLMConfig llmConfig) {
        this.llmConfig = llmConfig;
    }
}
