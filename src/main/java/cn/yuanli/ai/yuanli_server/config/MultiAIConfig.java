package cn.yuanli.ai.yuanli_server.config;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 多AI平台配置类
 * 支持OpenAI、DeepSeek、XAI等多个AI平台
 */
@Configuration
public class MultiAIConfig {

    // OpenAI配置
    @Value("${spring.ai.openai.api-key:}")
    private String openaiApiKey;
    
    @Value("${spring.ai.openai.base-url:https://api.openai.com}")
    private String openaiBaseUrl;
    
    @Value("${spring.ai.openai.chat.options.model:gpt-4o-mini}")
    private String openaiModel;

    // DeepSeek配置
    @Value("${spring.ai.deepseek.api-key:}")
    private String deepseekApiKey;
    
    @Value("${spring.ai.deepseek.base-url:https://api.deepseek.com}")
    private String deepseekBaseUrl;
    
    @Value("${spring.ai.deepseek.chat.options.model:deepseek-chat}")
    private String deepseekModel;

    // XAI配置
    @Value("${spring.ai.xai.api-key:}")
    private String xaiApiKey;
    
    @Value("${spring.ai.xai.base-url:https://api.x.ai}")
    private String xaiBaseUrl;
    
    @Value("${spring.ai.xai.chat.options.model:grok-beta}")
    private String xaiModel;

    // 注意：由于Spring AI M6版本与Spring AI Alibaba的兼容性问题，
    // 暂时注释掉OpenAI兼容的配置。
    // 用户可以在配置文件中设置API密钥，但需要等待版本兼容性问题解决。
}
