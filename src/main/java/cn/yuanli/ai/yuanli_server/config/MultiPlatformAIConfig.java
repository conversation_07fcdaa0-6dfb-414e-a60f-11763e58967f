package cn.yuanli.ai.yuanli_server.config;

import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.HashMap;
import java.util.Map;

/**
 * 多平台AI配置类
 * 基于Spring AI原生支持，通过OpenAI兼容接口支持多个平台
 */
@Configuration
public class MultiPlatformAIConfig {

    // OpenAI配置
    @Value("${spring.ai.openai.api-key}")
    private String openaiApiKey;
    
    @Value("${spring.ai.openai.base-url:https://api.openai.com}")
    private String openaiBaseUrl;

    // DeepSeek配置
    @Value("${app.ai.deepseek.api-key}")
    private String deepseekApiKey;
    
    @Value("${app.ai.deepseek.base-url}")
    private String deepseekBaseUrl;
    
    @Value("${app.ai.deepseek.model}")
    private String deepseekModel;

    // XAI配置
    @Value("${app.ai.xai.api-key}")
    private String xaiApiKey;
    
    @Value("${app.ai.xai.base-url}")
    private String xaiBaseUrl;
    
    @Value("${app.ai.xai.model}")
    private String xaiModel;

    // DashScope配置
    @Value("${app.ai.dashscope.api-key}")
    private String dashscopeApiKey;
    
    @Value("${app.ai.dashscope.base-url}")
    private String dashscopeBaseUrl;
    
    @Value("${app.ai.dashscope.model}")
    private String dashscopeModel;

    /**
     * 主要的OpenAI ChatModel (默认)
     */
    @Bean
    @Primary
    public ChatModel openaiChatModel() {
        return new OpenAiChatModel(
            new OpenAiApi(openaiBaseUrl, openaiApiKey),
            OpenAiChatOptions.builder()
                .model("gpt-4o-mini")
                .temperature(0.7)
                .maxTokens(2000)
                .build()
        );
    }

    /**
     * DeepSeek ChatModel (通过OpenAI兼容接口)
     */
    @Bean("deepseekChatModel")
    public ChatModel deepseekChatModel() {
        return new OpenAiChatModel(
            new OpenAiApi(deepseekBaseUrl, deepseekApiKey),
            OpenAiChatOptions.builder()
                .model(deepseekModel)
                .temperature(0.7)
                .maxTokens(2000)
                .build()
        );
    }

    /**
     * XAI ChatModel (通过OpenAI兼容接口)
     */
    @Bean("xaiChatModel")
    public ChatModel xaiChatModel() {
        return new OpenAiChatModel(
            new OpenAiApi(xaiBaseUrl, xaiApiKey),
            OpenAiChatOptions.builder()
                .model(xaiModel)
                .temperature(0.7)
                .maxTokens(2000)
                .build()
        );
    }

    /**
     * DashScope ChatModel (通过OpenAI兼容接口)
     */
    @Bean("dashscopeChatModel")
    public ChatModel dashscopeChatModel() {
        return new OpenAiChatModel(
            new OpenAiApi(dashscopeBaseUrl, dashscopeApiKey),
            OpenAiChatOptions.builder()
                .model(dashscopeModel)
                .temperature(0.7)
                .maxTokens(2000)
                .build()
        );
    }

    /**
     * 平台ChatModel映射
     */
    @Bean("platformChatModels")
    public Map<String, ChatModel> platformChatModels() {
        Map<String, ChatModel> models = new HashMap<>();
        models.put("openai", openaiChatModel());
        models.put("deepseek", deepseekChatModel());
        models.put("xai", xaiChatModel());
        models.put("dashscope", dashscopeChatModel());
        return models;
    }
}
