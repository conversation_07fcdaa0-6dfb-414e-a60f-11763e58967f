package cn.yuanli.ai.yuanli_server.service;

import cn.yuanli.ai.yuanli_server.dto.openai.*;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.AssistantMessage;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.ai.openai.api.OpenAiApi.FunctionTool;
import reactor.core.publisher.Flux;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.model.tool.ToolCallingChatOptions;


/**
 * 标准化AI聊天服务
 * 基于Spring AI原生支持，提供真正的多平台集成
 */
@Service
public class MultiAIService {

    @Value("${app.ai.default-provider:openai}")
    private String defaultProvider;

    // 注入各平台的API Key
    @Value("${spring.ai.openai.api-key:}")
    private String openaiApiKey;

    @Value("${app.ai.deepseek.api-key:}")
    private String deepseekApiKey;

    @Value("${app.ai.xai.api-key:}")
    private String xaiApiKey;

    @Value("${app.ai.dashscope.api-key:}")
    private String dashscopeApiKey;

    private final Map<String, ChatModel> platformChatModels;
    private final ModelMappingService modelMappingService;
    private final ObjectMapper objectMapper;

    public MultiAIService(@Qualifier("platformChatModels") Map<String, ChatModel> platformChatModels,
                         ModelMappingService modelMappingService,
                         ObjectMapper objectMapper) {
        this.platformChatModels = platformChatModels;
        this.modelMappingService = modelMappingService;
        this.objectMapper = objectMapper;
    }

    /**
     * OpenAI标准聊天接口
     * 支持模型映射、用户自定义配置、function calling
     */
    public OpenAIChatResponse chat(OpenAIChatRequest request) {
        try {
            // 1. 解析模型和提供商
            ModelMappingService.ModelInfo modelInfo = resolveModel(request);

            // 2. 获取ChatModel
            ChatModel chatModel = resolveChatModel(request, modelInfo);

            // 3. 构建Prompt
            Prompt prompt = buildPrompt(request);

            // 4. 调用AI模型
            ChatResponse response = chatModel.call(prompt);

            // 5. 转换为OpenAI标准响应
            return convertToOpenAIResponse(response, modelInfo, request);

        } catch (Exception e) {
            throw new RuntimeException("聊天请求处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * OpenAI标准流式聊天接口
     * 支持模型映射、用户自定义配置、function calling
     */
    public Flux<String> chatStream(OpenAIChatRequest request) {
        try {
            // 1. 解析模型和提供商
            ModelMappingService.ModelInfo modelInfo = resolveModel(request);

            // 2. 获取ChatModel
            ChatModel chatModel = resolveChatModel(request, modelInfo);

            // 3. 构建Prompt
            Prompt prompt = buildPrompt(request);

            // 4. 调用AI模型流式接口
            Flux<ChatResponse> responseFlux = chatModel.stream(prompt);

            // 5. 转换为OpenAI标准SSE格式
            return responseFlux.map(response -> convertToOpenAIStreamChunk(response, modelInfo, request));

        } catch (Exception e) {
            return Flux.error(new RuntimeException("流式聊天请求处理失败: " + e.getMessage(), e));
        }
    }







    /**
     * 解析模型信息
     */
    private ModelMappingService.ModelInfo resolveModel(OpenAIChatRequest request) {
        // 优先使用用户自定义配置
        if (request.getLlmConfig() != null && request.getLlmConfig().isValid()) {
            LLMConfig config = request.getLlmConfig();

            // 验证提供商是否支持
            if (!modelMappingService.isProviderSupported(config.getUserProvider())) {
                throw new IllegalArgumentException("不支持的提供商: " + config.getUserProvider() +
                    ". 支持的提供商: " + modelMappingService.getSupportedProviders());
            }

            return new ModelMappingService.ModelInfo(config.getUserProvider(), config.getUserModel());
        }
        // 检查是否提供了模型参数
        if (request.getModel() == null || request.getModel().trim().isEmpty()) {
            throw new IllegalArgumentException("必须提供model参数或llmConfig自定义配置");
        }

        // 使用模型映射表
        return modelMappingService.getModelInfo(request.getModel());
    }

    /**
     * 解析ChatModel - 统一使用动态创建，确保正确处理tools参数
     */
    private ChatModel resolveChatModel(OpenAIChatRequest request, ModelMappingService.ModelInfo modelInfo) {
        // 统一使用动态创建ChatModel，确保tools参数和透传模式正确处理
        if (request.getLlmConfig() != null && request.getLlmConfig().isValid()) {
            // 使用用户自定义配置
            return createDynamicChatModel(request.getLlmConfig(), request);
        } else {
            // 使用默认配置，但仍然动态创建以支持tools参数
            return createDynamicChatModelFromDefaults(modelInfo, request);
        }
    }

    /**
     * 构建Prompt (支持function calling)
     */
    private Prompt buildPrompt(OpenAIChatRequest request) {
        List<Message> messages = new ArrayList<>();

        // 转换消息格式
        for (ChatMessage chatMessage : request.getMessages()) {
            Message message = convertToSpringAIMessage(chatMessage);
            if (message != null) {
                messages.add(message);
            }
        }

        // 构建ChatOptions (支持function calling)
        ChatOptions chatOptions = buildChatOptionsWithTools(request);

        return new Prompt(messages, chatOptions);
    }

    /**
     * 构建ChatOptions - 统一禁用内部tool处理，实现透传模式
     */
    private ChatOptions buildChatOptionsWithTools(OpenAIChatRequest request) {
        // 统一使用ToolCallingChatOptions并禁用内部工具执行
        // 这样不管有没有tools参数，都实现透传模式
        ToolCallingChatOptions.Builder optionsBuilder = ToolCallingChatOptions.builder();

        // 设置基本参数
        if (request.getTemperature() != null) {
            optionsBuilder.temperature(request.getTemperature());
        }
        if (request.getMaxTokens() != null) {
            optionsBuilder.maxTokens(request.getMaxTokens());
        }

        // 关键：统一禁用内部工具执行，实现透传模式
        optionsBuilder.internalToolExecutionEnabled(false);

        return optionsBuilder.build();
    }

    /**
     * 转换消息格式 (透传模式 - 只处理基本消息类型)
     */
    private Message convertToSpringAIMessage(ChatMessage chatMessage) {
        switch (chatMessage.getRole().toLowerCase()) {
            case "system":
                return new SystemMessage(chatMessage.getContent());
            case "user":
                return new UserMessage(chatMessage.getContent());
            case "assistant":
                // 透传模式：只处理基本assistant消息，不处理tool_calls
                return new AssistantMessage(chatMessage.getContent());
            default:
                // 透传模式：不处理tool和function角色消息
                return null;
        }
    }

    /**
     * 转换Tools为Spring AI格式
     */
    private List<FunctionTool> convertToSpringAITools(List<Tool> tools) {
        return tools.stream()
            .filter(tool -> "function".equals(tool.getType()))
            .map(tool -> {
                FunctionDefinition func = tool.getFunction();
                try {
                    String parametersJson = objectMapper.writeValueAsString(func.getParameters());

                    // Spring AI FunctionTool.Function构造函数参数顺序：description, name, parameters
                    FunctionTool.Function springAIFunction =
                        new FunctionTool.Function(
                            func.getDescription(),
                            func.getName(),
                            parametersJson
                        );

                    return new FunctionTool(springAIFunction);
                } catch (Exception e) {
                    throw new RuntimeException("Failed to serialize function parameters", e);
                }
            })
            .collect(Collectors.toList());
    }

    /**
     * 转换Functions为Spring AI格式 (兼容旧版)
     */
    private List<FunctionTool> convertFunctionsToSpringAITools(List<FunctionDefinition> functions) {
        return functions.stream()
            .map(func -> {
                try {
                    String parametersJson = objectMapper.writeValueAsString(func.getParameters());
                    return new FunctionTool(
                        new FunctionTool.Function(
                            func.getDescription(),
                            func.getName(),
                            parametersJson
                        )
                    );
                } catch (Exception e) {
                    throw new RuntimeException("Failed to serialize function parameters", e);
                }
            })
            .collect(Collectors.toList());
    }

    /**
     * 转换ToolChoice
     */
    private String convertToolChoice(Object toolChoice) {
        // 直接返回，Spring AI会处理
        return String.valueOf(toolChoice);
    }

    /**
     * 转换FunctionCall (兼容旧版)
     */
    private String convertFunctionCall(Object functionCall) {
        // 直接返回，Spring AI会处理
        return String.valueOf(functionCall);
    }



    /**
     * 转换为OpenAI标准响应
     */
    private OpenAIChatResponse convertToOpenAIResponse(ChatResponse response,
                                                      ModelMappingService.ModelInfo modelInfo,
                                                      OpenAIChatRequest request) {
        OpenAIChatResponse openAIResponse = new OpenAIChatResponse();

        // 基本信息
        openAIResponse.setId("chatcmpl-" + UUID.randomUUID().toString().replace("-", ""));
        openAIResponse.setObject("chat.completion");
        openAIResponse.setCreated(System.currentTimeMillis() / 1000);

        // 设置模型名称：优先使用自定义配置中的模型，否则使用请求中的模型或默认模型
        String modelName;
        if (request.getLlmConfig() != null && request.getLlmConfig().isValid()) {
            modelName = request.getLlmConfig().getUserModel();
        } else {
            modelName = request.getModel() != null ? request.getModel() : modelInfo.getModel();
        }
        openAIResponse.setModel(modelName);

        // 构建选择
        List<OpenAIChatResponse.Choice> choices = new ArrayList<>();
        if (response.getResult() != null) {
            ChatMessage assistantMessage = new ChatMessage("assistant",
                response.getResult().getOutput().getText());

            // 处理tool_calls - 透传模式，不执行函数
            String finishReason = "stop";
            if (response.getResult().getOutput() instanceof AssistantMessage) {
                AssistantMessage springAssistantMessage = (AssistantMessage) response.getResult().getOutput();
                if (springAssistantMessage.getToolCalls() != null && !springAssistantMessage.getToolCalls().isEmpty()) {
                    List<ToolCall> toolCalls = new ArrayList<>();
                    for (AssistantMessage.ToolCall springToolCall : springAssistantMessage.getToolCalls()) {
                        ToolCall toolCall = new ToolCall();
                        toolCall.setId(springToolCall.id());
                        toolCall.setType("function");

                        FunctionCall functionCall = new FunctionCall();
                        functionCall.setName(springToolCall.name());
                        functionCall.setArguments(springToolCall.arguments());
                        toolCall.setFunction(functionCall);

                        toolCalls.add(toolCall);
                    }
                    assistantMessage.setToolCalls(toolCalls);
                    finishReason = "tool_calls";
                }
            }

            OpenAIChatResponse.Choice choice = new OpenAIChatResponse.Choice(0,
                assistantMessage, finishReason);
            choices.add(choice);
        }
        openAIResponse.setChoices(choices);

        // 使用情况 (Spring AI Alibaba可能不提供详细的token统计)
        OpenAIChatResponse.Usage usage = new OpenAIChatResponse.Usage(0, 0, 0);
        openAIResponse.setUsage(usage);

        return openAIResponse;
    }

    /**
     * 转换为OpenAI标准流式响应块 (SSE格式)
     */
    private String convertToOpenAIStreamChunk(ChatResponse response,
                                            ModelMappingService.ModelInfo modelInfo,
                                            OpenAIChatRequest request) {
        // 构建流式响应块
        Map<String, Object> chunk = new HashMap<>();
        chunk.put("id", "chatcmpl-" + UUID.randomUUID().toString().replace("-", ""));
        chunk.put("object", "chat.completion.chunk");
        chunk.put("created", System.currentTimeMillis() / 1000);

        // 设置模型名称
        String modelName;
        if (request.getLlmConfig() != null && request.getLlmConfig().isValid()) {
            modelName = request.getLlmConfig().getUserModel();
        } else {
            modelName = request.getModel() != null ? request.getModel() : modelInfo.getModel();
        }
        chunk.put("model", modelName);

        // 构建choices
        List<Map<String, Object>> choices = new ArrayList<>();
        if (response.getResult() != null) {
            Map<String, Object> choice = new HashMap<>();
            choice.put("index", 0);

            Map<String, Object> delta = new HashMap<>();
            delta.put("role", "assistant");
            delta.put("content", response.getResult().getOutput().getText());

            // 处理tool_calls - 透传模式，不执行函数
            String finishReason = null; // 流式过程中为null，最后一块为"stop"或"tool_calls"
            if (response.getResult().getOutput() instanceof AssistantMessage) {
                AssistantMessage springAssistantMessage = (AssistantMessage) response.getResult().getOutput();
                if (springAssistantMessage.getToolCalls() != null && !springAssistantMessage.getToolCalls().isEmpty()) {
                    List<Map<String, Object>> toolCalls = new ArrayList<>();
                    for (AssistantMessage.ToolCall springToolCall : springAssistantMessage.getToolCalls()) {
                        Map<String, Object> toolCall = new HashMap<>();
                        toolCall.put("id", springToolCall.id());
                        toolCall.put("type", "function");

                        Map<String, Object> function = new HashMap<>();
                        function.put("name", springToolCall.name());
                        function.put("arguments", springToolCall.arguments());
                        toolCall.put("function", function);

                        toolCalls.add(toolCall);
                    }
                    delta.put("tool_calls", toolCalls);
                    finishReason = "tool_calls";
                }
            }

            choice.put("delta", delta);
            choice.put("finish_reason", finishReason);
            choices.add(choice);
        }
        chunk.put("choices", choices);

        // 转换为SSE格式
        try {
            String json = objectMapper.writeValueAsString(chunk);
            return "data: " + json + "\n\n";
        } catch (Exception e) {
            return "data: {\"error\": \"JSON序列化失败\"}\n\n";
        }
    }

    /**
     * 根据用户自定义配置创建动态ChatModel
     */
    private ChatModel createDynamicChatModel(LLMConfig config, OpenAIChatRequest request) {
        // 优先使用用户提供的baseUrl，否则根据provider获取默认baseUrl
        String baseUrl = config.getUserBaseUrl() != null && !config.getUserBaseUrl().trim().isEmpty()
            ? config.getUserBaseUrl()
            : getBaseUrlForProvider(config.getUserProvider());

        return createChatModelWithTools(
            baseUrl,
            config.getUserApiKey(),
            config.getUserModel(),
            request
        );
    }

    /**
     * 根据默认配置创建动态ChatModel
     */
    private ChatModel createDynamicChatModelFromDefaults(ModelMappingService.ModelInfo modelInfo, OpenAIChatRequest request) {
        String provider = modelInfo.getProvider();
        String baseUrl = getBaseUrlForProvider(provider);
        String apiKey = getApiKeyForProvider(provider);
        String model = modelInfo.getModel();

        return createChatModelWithTools(baseUrl, apiKey, model, request);
    }

    /**
     * 统一的ChatModel创建方法，支持tools参数
     */
    private ChatModel createChatModelWithTools(String baseUrl, String apiKey, String model, OpenAIChatRequest request) {
        OpenAiChatOptions.Builder optionsBuilder = OpenAiChatOptions.builder()
            .model(model)
            .temperature(0.7)
            .maxTokens(2000);

        // 支持function calling - 使用tools参数 (新版本)
        if (request.getTools() != null && !request.getTools().isEmpty()) {
            List<FunctionTool> springAITools =
                convertToSpringAITools(request.getTools());
            optionsBuilder.tools(springAITools);

            // 设置tool choice
            if (request.getToolChoice() != null) {
                optionsBuilder.toolChoice(convertToolChoice(request.getToolChoice()));
            }
        }

        // 支持旧版function calling - 使用functions参数
        else if (request.getFunctions() != null && !request.getFunctions().isEmpty()) {
            List<FunctionTool> springAIFunctions =
                convertFunctionsToSpringAITools(request.getFunctions());
            optionsBuilder.tools(springAIFunctions);

            // 设置function call
            if (request.getFunctionCall() != null) {
                optionsBuilder.toolChoice(convertFunctionCall(request.getFunctionCall()));
            }
        }

        return new OpenAiChatModel(
            new OpenAiApi(baseUrl, apiKey),
            optionsBuilder.build()
        );
    }





    /**
     * 根据提供商获取对应的base URL
     */
    private String getBaseUrlForProvider(String provider) {
        return switch (provider.toLowerCase()) {
            case "openai" -> "https://api.openai.com";
            case "deepseek" -> "https://api.deepseek.com";
            case "xai" -> "https://api.x.ai";
            case "dashscope" -> "https://dashscope.aliyuncs.com/compatible-mode/v1";
            default -> throw new IllegalArgumentException("不支持的提供商: " + provider);
        };
    }

    /**
     * 根据提供商获取对应的API Key
     */
    private String getApiKeyForProvider(String provider) {
        return switch (provider.toLowerCase()) {
            case "openai" -> openaiApiKey;
            case "deepseek" -> deepseekApiKey;
            case "xai" -> xaiApiKey;
            case "dashscope" -> dashscopeApiKey;
            default -> throw new IllegalArgumentException("不支持的提供商: " + provider);
        };
    }


}
