package cn.yuanli.ai.yuanli_server.service;

import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 模型映射服务
 * 维护模型名称到实际提供商和模型的映射关系
 */
@Service
public class ModelMappingService {
    
    /**
     * 模型映射表
     * Key: 对外暴露的模型名称
     * Value: ModelInfo对象，包含实际的provider和model
     */
    private final Map<String, ModelInfo> modelMappings = new HashMap<>();
    
    /**
     * 支持的提供商列表
     */
    private final Set<String> supportedProviders = Set.of(
        "openai", "deepseek", "xai", "dashscope", "ollama"
    );
    
    public ModelMappingService() {
        initializeModelMappings();
    }
    
    /**
     * 初始化模型映射表
     */
    private void initializeModelMappings() {
        // OpenAI模型映射
        modelMappings.put("gpt-4o", new ModelInfo("openai", "gpt-4o"));
        modelMappings.put("gpt-4o-mini", new ModelInfo("openai", "gpt-4o-mini"));
        modelMappings.put("gpt-4", new ModelInfo("openai", "gpt-4"));
        modelMappings.put("gpt-3.5-turbo", new ModelInfo("openai", "gpt-3.5-turbo"));

        // DeepSeek模型映射
        modelMappings.put("deepseek-chat", new ModelInfo("deepseek", "deepseek-chat"));
        modelMappings.put("deepseek-coder", new ModelInfo("deepseek", "deepseek-coder"));

        // XAI模型映射
        modelMappings.put("grok-3", new ModelInfo("xai", "grok-3"));
        modelMappings.put("grok-beta", new ModelInfo("xai", "grok-3")); // 兼容旧名称
        modelMappings.put("grok-vision-beta", new ModelInfo("xai", "grok-vision-beta"));

        // DashScope模型映射
        modelMappings.put("qwen-plus", new ModelInfo("dashscope", "qwen-plus"));
        modelMappings.put("qwen-turbo", new ModelInfo("dashscope", "qwen-turbo"));
        modelMappings.put("qwen-max", new ModelInfo("dashscope", "qwen-max"));
        modelMappings.put("qwen-long", new ModelInfo("dashscope", "qwen-long"));

        // Ollama模型映射
        modelMappings.put("llama3.2", new ModelInfo("ollama", "llama3.2"));
        modelMappings.put("llama3.1", new ModelInfo("ollama", "llama3.1"));
        modelMappings.put("qwen2.5", new ModelInfo("ollama", "qwen2.5"));

        // 通用别名映射
        modelMappings.put("default", new ModelInfo("openai", "gpt-4o-mini"));
        modelMappings.put("fast", new ModelInfo("openai", "gpt-3.5-turbo"));
        modelMappings.put("smart", new ModelInfo("openai", "gpt-4o"));
        modelMappings.put("coding", new ModelInfo("deepseek", "deepseek-coder"));
        modelMappings.put("local", new ModelInfo("ollama", "llama3.2"));
    }
    
    /**
     * 根据模型名称获取模型信息
     */
    public ModelInfo getModelInfo(String modelName) {
        if (modelName == null || modelName.trim().isEmpty()) {
            return modelMappings.get("default");
        }
        return modelMappings.getOrDefault(modelName.trim(), modelMappings.get("default"));
    }
    
    /**
     * 检查模型是否存在
     */
    public boolean isModelSupported(String modelName) {
        return modelMappings.containsKey(modelName);
    }
    
    /**
     * 获取所有支持的模型列表
     */
    public Set<String> getSupportedModels() {
        return modelMappings.keySet();
    }
    
    /**
     * 检查提供商是否支持
     */
    public boolean isProviderSupported(String provider) {
        return supportedProviders.contains(provider);
    }
    
    /**
     * 获取所有支持的提供商
     */
    public Set<String> getSupportedProviders() {
        return supportedProviders;
    }
    
    /**
     * 根据提供商获取该提供商的所有模型
     */
    public Map<String, String> getModelsByProvider(String provider) {
        Map<String, String> result = new HashMap<>();
        modelMappings.entrySet().stream()
            .filter(entry -> entry.getValue().getProvider().equals(provider))
            .forEach(entry -> result.put(entry.getKey(), entry.getValue().getModel()));
        return result;
    }
    
    /**
     * 模型信息类
     */
    public static class ModelInfo {
        private final String provider;
        private final String model;
        
        public ModelInfo(String provider, String model) {
            this.provider = provider;
            this.model = model;
        }
        
        public String getProvider() {
            return provider;
        }
        
        public String getModel() {
            return model;
        }
        
        @Override
        public String toString() {
            return String.format("ModelInfo{provider='%s', model='%s'}", provider, model);
        }
    }
}
