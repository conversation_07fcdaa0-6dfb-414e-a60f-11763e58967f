package cn.yuanli.ai.yuanli_server.controller;

import cn.yuanli.ai.yuanli_server.dto.ChatRequest;
import cn.yuanli.ai.yuanli_server.dto.ChatResponse;
import cn.yuanli.ai.yuanli_server.service.MultiAIService;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 多AI平台聊天控制器
 * 支持DashScope、OpenAI、DeepSeek、XAI等多个AI平台
 */
@RestController
@RequestMapping("/api/chat")
public class ChatController {

    private final MultiAIService multiAIService;

    public ChatController(MultiAIService multiAIService) {
        this.multiAIService = multiAIService;
    }

    /**
     * 简单聊天接口 - 使用默认AI平台
     * @param request 聊天请求
     * @return AI回复
     */
    @PostMapping("/simple")
    public cn.yuanli.ai.yuanli_server.dto.ChatResponse simpleChat(@RequestBody ChatRequest request) {
        String aiResponse = multiAIService.chat(request.getMessage());
        return new cn.yuanli.ai.yuanli_server.dto.ChatResponse(aiResponse, multiAIService.getDefaultProvider());
    }

    /**
     * 简单聊天接口 - 指定AI平台
     * @param request 聊天请求
     * @param provider AI平台 (dashscope/openai/deepseek/xai)
     * @return AI回复
     */
    @PostMapping("/simple/{provider}")
    public cn.yuanli.ai.yuanli_server.dto.ChatResponse simpleChatWithProvider(
            @RequestBody ChatRequest request,
            @PathVariable String provider) {
        String aiResponse = multiAIService.chat(request.getMessage(), provider);
        return new cn.yuanli.ai.yuanli_server.dto.ChatResponse(aiResponse, provider);
    }

    /**
     * 详细聊天接口 - 使用默认AI平台
     * @param request 聊天请求
     * @return 完整的AI响应
     */
    @PostMapping("/detailed")
    public org.springframework.ai.chat.model.ChatResponse detailedChat(@RequestBody ChatRequest request) {
        return multiAIService.detailedChat(request.getMessage());
    }

    /**
     * 详细聊天接口 - 指定AI平台
     * @param request 聊天请求
     * @param provider AI平台 (dashscope/openai/deepseek/xai)
     * @return 完整的AI响应
     */
    @PostMapping("/detailed/{provider}")
    public org.springframework.ai.chat.model.ChatResponse detailedChatWithProvider(
            @RequestBody ChatRequest request,
            @PathVariable String provider) {
        return multiAIService.detailedChat(request.getMessage(), provider);
    }

    /**
     * 获取可用的AI平台列表
     * @return 可用平台信息
     */
    @GetMapping("/providers")
    public Map<String, Object> getProviders() {
        Map<String, Object> result = Map.of(
            "available", multiAIService.getAvailableProviders(),
            "default", multiAIService.getDefaultProvider()
        );
        return result;
    }

    /**
     * 健康检查接口
     * @return 状态信息
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        return Map.of(
            "status", "running",
            "message", "多AI平台聊天控制器运行正常",
            "availableProviders", multiAIService.getAvailableProviders(),
            "defaultProvider", multiAIService.getDefaultProvider()
        );
    }
}
