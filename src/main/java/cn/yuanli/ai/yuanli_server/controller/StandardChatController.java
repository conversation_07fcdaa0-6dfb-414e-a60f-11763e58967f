package cn.yuanli.ai.yuanli_server.controller;

import cn.yuanli.ai.yuanli_server.dto.openai.*;
import cn.yuanli.ai.yuanli_server.service.MultiAIService;
import cn.yuanli.ai.yuanli_server.service.ModelMappingService;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;

/**
 * 标准化聊天控制器
 * 完全按照OpenAI协议设计，支持模型映射和用户自定义配置
 */
@RestController
@RequestMapping("/v1")
public class StandardChatController {

    private final MultiAIService multiAIService;
    private final ModelMappingService modelMappingService;

    public StandardChatController(MultiAIService multiAIService, ModelMappingService modelMappingService) {
        this.multiAIService = multiAIService;
        this.modelMappingService = modelMappingService;
    }

    /**
     * OpenAI标准聊天完成接口 (同步)
     * POST /v1/chat/completions
     */
    @PostMapping("/chat/completions")
    public OpenAIChatResponse chatCompletions(@RequestBody OpenAIChatRequest request) {
        // 验证请求
        validateRequest(request);

        // 如果请求流式响应，返回错误提示
        if (Boolean.TRUE.equals(request.getStream())) {
            throw new IllegalArgumentException("流式响应请使用stream=true参数并调用流式接口");
        }

        // 同步响应
        return multiAIService.chat(request);
    }

    /**
     * OpenAI标准聊天完成接口 (流式)
     * POST /v1/chat/completions (当stream=true时)
     */
    @PostMapping(value = "/chat/completions", params = "stream=true", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> chatCompletionsStream(@RequestBody OpenAIChatRequest request) {
        // 验证请求
        validateRequest(request);

        // 流式响应
        return multiAIService.chatStream(request)
            .concatWith(Flux.just("data: [DONE]\n\n")); // 添加结束标记
    }

    /**
     * 获取支持的模型列表
     * GET /v1/models
     */
    @GetMapping("/models")
    public Map<String, Object> getModels() {
        return Map.of(
            "object", "list",
            "data", modelMappingService.getSupportedModels().stream()
                .map(model -> {
                    ModelMappingService.ModelInfo info = modelMappingService.getModelInfo(model);
                    return Map.of(
                        "id", model,
                        "object", "model",
                        "provider", info.getProvider(),
                        "actual_model", info.getModel()
                    );
                }).toList()
        );
    }

    /**
     * 获取模型详细信息
     * GET /v1/models/{model}
     */
    @GetMapping("/models/{model}")
    public Map<String, Object> getModel(@PathVariable String model) {
        if (!modelMappingService.isModelSupported(model)) {
            throw new IllegalArgumentException("不支持的模型: " + model);
        }
        
        ModelMappingService.ModelInfo info = modelMappingService.getModelInfo(model);
        return Map.of(
            "id", model,
            "object", "model",
            "provider", info.getProvider(),
            "actual_model", info.getModel(),
            "supported_providers", modelMappingService.getSupportedProviders()
        );
    }

    /**
     * 健康检查接口
     * GET /v1/health
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        return Map.of(
            "status", "ok",
            "service", "Standard AI Chat Service",
            "supported_models", modelMappingService.getSupportedModels().size(),
            "supported_providers", modelMappingService.getSupportedProviders(),
            "features", List.of(
                "OpenAI Compatible API",
                "Model Mapping",
                "Custom LLM Config",
                "Function Calling Ready"
            )
        );
    }

    /**
     * 获取提供商信息
     * GET /v1/providers
     */
    @GetMapping("/providers")
    public Map<String, Object> getProviders() {
        Map<String, Object> result = Map.of(
            "supported_providers", modelMappingService.getSupportedProviders(),
            "provider_models", modelMappingService.getSupportedProviders().stream()
                .collect(java.util.stream.Collectors.toMap(
                    provider -> provider,
                    provider -> modelMappingService.getModelsByProvider(provider)
                ))
        );
        return result;
    }

    /**
     * 验证请求参数
     */
    private void validateRequest(OpenAIChatRequest request) {
        if (request.getMessages() == null || request.getMessages().isEmpty()) {
            throw new IllegalArgumentException("messages参数不能为空");
        }

        // 验证消息格式
        for (ChatMessage message : request.getMessages()) {
            if (message.getRole() == null || message.getRole().trim().isEmpty()) {
                throw new IllegalArgumentException("消息角色不能为空");
            }
            if (message.getContent() == null || message.getContent().trim().isEmpty()) {
                throw new IllegalArgumentException("消息内容不能为空");
            }
        }

        // 验证模型 (如果有自定义配置，则跳过模型验证，因为自定义配置优先级更高)
        if (request.getLlmConfig() == null || !request.getLlmConfig().isValid()) {
            if (request.getModel() != null && !modelMappingService.isModelSupported(request.getModel())) {
                throw new IllegalArgumentException("不支持的模型: " + request.getModel() +
                    ". 支持的模型: " + modelMappingService.getSupportedModels());
            }
        }

        // 验证用户自定义配置
        if (request.getLlmConfig() != null) {
            LLMConfig config = request.getLlmConfig();
            if (config.getUserProvider() != null && 
                !modelMappingService.isProviderSupported(config.getUserProvider())) {
                throw new IllegalArgumentException("不支持的提供商: " + config.getUserProvider() + 
                    ". 支持的提供商: " + modelMappingService.getSupportedProviders());
            }
        }

        // 验证参数范围
        if (request.getTemperature() != null && 
            (request.getTemperature() < 0 || request.getTemperature() > 2)) {
            throw new IllegalArgumentException("temperature参数必须在0-2之间");
        }

        if (request.getMaxTokens() != null && request.getMaxTokens() <= 0) {
            throw new IllegalArgumentException("max_tokens参数必须大于0");
        }
    }

    /**
     * 全局异常处理
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public Map<String, Object> handleIllegalArgument(IllegalArgumentException e) {
        return Map.of(
            "error", Map.of(
                "type", "invalid_request_error",
                "message", e.getMessage(),
                "code", "invalid_parameter"
            )
        );
    }

    @ExceptionHandler(UnsupportedOperationException.class)
    public Map<String, Object> handleUnsupportedOperation(UnsupportedOperationException e) {
        return Map.of(
            "error", Map.of(
                "type", "unsupported_operation",
                "message", e.getMessage(),
                "code", "feature_not_available"
            )
        );
    }

    @ExceptionHandler(Exception.class)
    public Map<String, Object> handleGenericException(Exception e) {
        return Map.of(
            "error", Map.of(
                "type", "internal_error",
                "message", "服务内部错误: " + e.getMessage(),
                "code", "internal_server_error"
            )
        );
    }
}
