spring.application.name=yuanli_server

# ??Bean??????????AI??
spring.main.allow-bean-definition-overriding=true

# ========================================
# Spring AI ????? - ??????API Key
# ========================================

# 1. OpenAI?? (????)
spring.ai.openai.api-key=********************************************************************************************************************************************************************
spring.ai.openai.base-url=https://api.openai.com
spring.ai.openai.chat.options.model=gpt-4o-mini
spring.ai.openai.chat.options.temperature=0.7
spring.ai.openai.chat.options.max-tokens=2000

# 2. Ollama?? (????)
spring.ai.ollama.base-url=http://localhost:11434
spring.ai.ollama.chat.options.model=llama3.2
spring.ai.ollama.chat.options.temperature=0.7

# ========================================
# ????
# ========================================

# ??AI???
app.ai.default-provider=openai

# ????? (??OpenAI????)
# DeepSeek??
app.ai.deepseek.api-key=***********************************
app.ai.deepseek.base-url=https://api.deepseek.com
app.ai.deepseek.model=deepseek-chat

# XAI??
app.ai.xai.api-key=************************************************************************************
app.ai.xai.base-url=https://api.x.ai
app.ai.xai.model=grok-3

# DashScope?? (??OpenAI????)
app.ai.dashscope.api-key=sk-e4bffc041b044c2192629ab0eef0734f
app.ai.dashscope.base-url=https://dashscope.aliyuncs.com/compatible-mode/v1
app.ai.dashscope.model=qwen-plus

