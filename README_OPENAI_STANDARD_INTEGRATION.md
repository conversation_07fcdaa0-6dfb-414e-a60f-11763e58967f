# Spring AI Alibaba OpenAI标准集成指南

## 🎉 集成完成状态

✅ **已完成的功能**：
- Spring AI Alibaba 1.0.0.2 集成
- OpenAI标准API协议支持
- 模型映射表管理
- 用户自定义LLM配置
- Function Calling架构准备
- 多平台配置预留

## 📋 技术架构

### 1. 核心组件

**控制器层**：
- `StandardChatController` - OpenAI标准API控制器
- 路径：`/v1/chat/completions` (完全兼容OpenAI API)

**服务层**：
- `MultiAIService` - 标准化AI聊天服务
- `ModelMappingService` - 模型映射管理服务

**DTO层**：
- `OpenAIChatRequest` - OpenAI标准请求格式
- `OpenAIChatResponse` - OpenAI标准响应格式
- `ChatMessage` - 消息格式
- `LLMConfig` - 用户自定义配置
- `FunctionDefinition` - Function Calling支持

### 2. 模型映射表

系统维护了一个完整的模型映射表，支持以下模型：

**DashScope模型**：
- `qwen-plus` → dashscope/qwen-plus
- `qwen-turbo` → dashscope/qwen-turbo  
- `qwen-max` → dashscope/qwen-max
- `qwen-long` → dashscope/qwen-long

**OpenAI模型**：
- `gpt-4o` → openai/gpt-4o
- `gpt-4o-mini` → openai/gpt-4o-mini
- `gpt-4` → openai/gpt-4
- `gpt-3.5-turbo` → openai/gpt-3.5-turbo

**DeepSeek模型**：
- `deepseek-chat` → deepseek/deepseek-chat
- `deepseek-coder` → deepseek/deepseek-coder

**XAI模型**：
- `grok-beta` → xai/grok-beta
- `grok-vision-beta` → xai/grok-vision-beta

**通用别名**：
- `default` → dashscope/qwen-plus
- `fast` → dashscope/qwen-turbo
- `smart` → dashscope/qwen-max
- `coding` → deepseek/deepseek-coder

## 🚀 API接口

### 1. 聊天完成接口 (OpenAI标准)

```bash
POST /v1/chat/completions
Content-Type: application/json

{
  "model": "qwen-plus",
  "messages": [
    {"role": "user", "content": "你好，请介绍一下你自己"}
  ],
  "temperature": 0.7,
  "max_tokens": 1000,
  "llmConfig": {
    "userProvider": "dashscope",
    "userModel": "qwen-max", 
    "userApiKey": "your-custom-api-key"
  }
}
```

### 2. 模型列表接口

```bash
GET /v1/models
```

### 3. 提供商信息接口

```bash
GET /v1/providers
```

### 4. 健康检查接口

```bash
GET /v1/health
```

## ⚙️ 配置说明

### 当前配置文件 (application.properties)

```properties
# 阿里云百炼DashScope配置 (当前可用)
spring.ai.dashscope.api-key=your-dashscope-api-key-here
spring.ai.dashscope.chat.options.model=qwen-plus

# OpenAI配置 (已预留)
spring.ai.openai.api-key=your-openai-api-key-here
spring.ai.openai.base-url=https://api.openai.com
spring.ai.openai.chat.options.model=gpt-4o-mini

# DeepSeek配置 (已预留)  
spring.ai.deepseek.api-key=your-deepseek-api-key-here
spring.ai.deepseek.base-url=https://api.deepseek.com
spring.ai.deepseek.chat.options.model=deepseek-chat

# XAI配置 (已预留)
spring.ai.xai.api-key=your-xai-api-key-here
spring.ai.xai.base-url=https://api.x.ai
spring.ai.xai.chat.options.model=grok-beta
```

## 🔧 使用方法

### 1. 替换API密钥

将配置文件中的 `your-dashscope-api-key-here` 替换为真实的DashScope API密钥。

### 2. 启动应用

```bash
./mvnw spring-boot:run
```

### 3. 测试API

```bash
# 健康检查
curl -X GET http://localhost:8080/v1/health

# 查看支持的模型
curl -X GET http://localhost:8080/v1/models

# 聊天对话 (需要真实API密钥)
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "qwen-plus",
    "messages": [
      {"role": "user", "content": "你好"}
    ]
  }'
```

## 🎯 核心特性

### 1. OpenAI标准兼容

完全按照OpenAI API协议设计，支持：
- 标准的请求/响应格式
- 消息角色 (system, user, assistant)
- 温度、最大token等参数
- 错误处理格式

### 2. 模型映射

- 维护统一的模型映射表
- 支持模型别名 (如 `fast` → `qwen-turbo`)
- 自动解析provider和实际模型名称

### 3. 用户自定义配置

支持 `llmConfig` 参数：
```json
{
  "llmConfig": {
    "userProvider": "dashscope",
    "userModel": "qwen-max",
    "userApiKey": "your-custom-key"
  }
}
```

### 4. Function Calling准备

已创建完整的Function Calling架构：
- `FunctionDefinition` - 函数定义
- `Tool` - 工具定义
- `ToolCall` - 工具调用
- 支持新版tools API和旧版functions API

## ⚠️ 当前限制

1. **平台支持**：由于版本兼容性问题，目前只支持DashScope平台
2. **Function Calling**：架构已准备，等待Spring AI Alibaba完善支持
3. **其他平台**：配置已预留，等待版本兼容性问题解决

## 🔮 后续计划

1. **Function Calling实现**：一旦Spring AI Alibaba支持，立即启用
2. **多平台支持**：等待版本兼容性问题解决后启用所有平台
3. **流式响应**：添加Server-Sent Events支持
4. **嵌入模型**：集成文本嵌入功能

## 📞 获取API密钥

**DashScope (阿里云百炼)**：
1. 访问：https://dashscope.console.aliyun.com/
2. 注册并创建API密钥
3. 替换配置文件中的占位符

现在你的项目已经完全按照OpenAI标准实现，支持模型映射和用户自定义配置！🎉
